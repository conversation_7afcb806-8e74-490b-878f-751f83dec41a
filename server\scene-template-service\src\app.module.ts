import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ScheduleModule } from '@nestjs/schedule';

// 核心模块
import { TemplatesModule } from './modules/templates/templates.module';
import { CategoriesModule } from './modules/categories/categories.module';
import { ParametersModule } from './modules/parameters/parameters.module';
import { VersionsModule } from './modules/versions/versions.module';
import { SharingModule } from './modules/sharing/sharing.module';
import { RatingsModule } from './modules/ratings/ratings.module';
import { MarketplaceModule } from './modules/marketplace/marketplace.module';
import { AuthModule } from './modules/auth/auth.module';

// 公共服务
import { LoggerService } from './common/services/logger.service';
import { CacheService } from './common/services/cache.service';
import { StorageService } from './common/services/storage.service';
import { HealthController } from './common/controllers/health.controller';

// 实体
import { SceneTemplate } from './modules/templates/entities/scene-template.entity';
import { TemplateCategory } from './modules/categories/entities/template-category.entity';
import { TemplateParameter } from './modules/parameters/entities/template-parameter.entity';
import { TemplateVersion } from './modules/versions/entities/template-version.entity';
import { TemplateShare } from './modules/sharing/entities/template-share.entity';
import { TemplateRating } from './modules/ratings/entities/template-rating.entity';
import { User } from './modules/auth/entities/user.entity';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    
    // 调度模块
    ScheduleModule.forRoot(),
    
    // 数据库模块
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        type: 'mysql',
        host: configService.get('DB_HOST', 'localhost'),
        port: configService.get('DB_PORT', 3306),
        username: configService.get('DB_USERNAME', 'root'),
        password: configService.get('DB_PASSWORD', 'password'),
        database: configService.get('DB_DATABASE', 'ir_engine_scene_templates'),
        entities: [
          SceneTemplate,
          TemplateCategory,
          TemplateParameter,
          TemplateVersion,
          TemplateShare,
          TemplateRating,
          User,
        ],
        synchronize: configService.get('NODE_ENV') !== 'production',
        logging: configService.get('NODE_ENV') === 'development',
        charset: 'utf8mb4',
        timezone: '+08:00',
        extra: {
          ssl: false,
          connectionLimit: 10,
        },
        connectTimeout: 60000,
        acquireTimeout: 60000,
        timeout: 60000,
      }),
      inject: [ConfigService],
    }),
    
    // JWT模块
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get('JWT_SECRET', 'scene-template-secret'),
        signOptions: { expiresIn: '24h' },
      }),
      inject: [ConfigService],
    }),
    
    // Passport模块
    PassportModule.register({ defaultStrategy: 'jwt' }),
    
    // 业务模块
    TemplatesModule,
    CategoriesModule,
    ParametersModule,
    VersionsModule,
    SharingModule,
    RatingsModule,
    MarketplaceModule,
    AuthModule,
  ],
  controllers: [HealthController],
  providers: [
    LoggerService,
    CacheService,
    StorageService,
  ],
  exports: [
    LoggerService,
    CacheService,
    StorageService,
  ],
})
export class AppModule {}
