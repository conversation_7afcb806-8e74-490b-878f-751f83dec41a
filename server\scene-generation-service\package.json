{"name": "scene-generation-service", "version": "1.0.0", "description": "场景生成微服务，支持文本和语音输入生成3D场景，集成AI模型和资源库", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "start:dev": "ts-node src/main.ts", "start:debug": "ts-node --inspect src/main.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "docker:build": "docker build -t scene-generation-service .", "docker:run": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f scene-generation-service", "docker:system": "docker-compose -f docker-compose.system.yml up -d"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/websockets": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "redis": "^4.6.0", "uuid": "^9.0.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "helmet": "^7.0.0", "express-rate-limit": "^6.8.0", "winston": "^3.10.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "lodash": "^4.17.21", "joi": "^17.9.0", "axios": "^1.5.0", "socket.io": "^4.7.0", "three": "^0.156.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/express": "^4.17.17", "@types/uuid": "^9.0.0", "@types/bcryptjs": "^2.4.2", "@types/passport-jwt": "^3.0.8", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.195", "@types/multer": "^1.4.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.1.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.spec.ts", "!src/**/*.interface.ts"]}, "keywords": ["scene-generation", "3d-scenes", "ai-generation", "text-to-scene", "voice-to-scene", "microservice", "<PERSON><PERSON><PERSON>", "typescript"], "author": "DL Engine Team", "license": "MIT"}