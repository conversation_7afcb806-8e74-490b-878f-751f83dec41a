import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { IoAdapter } from '@nestjs/platform-socket.io';
import * as compression from 'compression';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';
import { LoggerService } from './common/services/logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // 获取配置服务
  const configService = app.get(ConfigService);
  const logger = app.get(LoggerService);
  
  // 全局中间件
  app.use(helmet());
  app.use(compression());
  
  // 限流配置
  app.use(
    rateLimit({
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 1000, // 限制每个IP 15分钟内最多1000个请求
      message: '请求过于频繁，请稍后再试',
    }),
  );
  
  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  
  // CORS配置
  const corsOrigins = configService.get<string>('CORS_ORIGIN')
    ? configService.get<string>('CORS_ORIGIN').split(',')
    : ['http://localhost:3000', 'http://localhost:80', 'http://localhost'];

  app.enableCors({
    origin: corsOrigins,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });
  
  // WebSocket适配器
  app.useWebSocketAdapter(new IoAdapter(app));
  
  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('场景生成微服务 API')
    .setDescription('支持文本和语音输入生成3D场景，集成AI模型和资源库的场景生成服务')
    .setVersion('1.0')
    .addBearerAuth()
    .addTag('generation', '场景生成')
    .addTag('tasks', '生成任务')
    .addTag('scenes', '场景管理')
    .addTag('templates', '模板管理')
    .addTag('assets', '资源管理')
    .addTag('ai-models', 'AI模型集成')
    .addTag('websocket', 'WebSocket实时通信')
    .build();
    
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  
  // 启动服务
  const port = configService.get('PORT', 8005);
  await app.listen(port, '0.0.0.0');

  logger.log(`🎨 场景生成微服务启动成功，端口: ${port}`);
  logger.log(`📚 Swagger文档地址: http://localhost:${port}/api/docs`);
  logger.log(`🏥 健康检查地址: http://localhost:${port}/health`);
  logger.log(`🌐 环境: ${configService.get('NODE_ENV', 'development')}`);
}

bootstrap().catch((error) => {
  console.error('服务启动失败:', error);
  process.exit(1);
});
